# 联系我们组件

## 概述
联系我们组件是一个可复用的UI组件，用于在网站的各个页面中显示联系我们的信息和按钮。

## 文件结构
```
component/contact/
├── contact.html    # 组件HTML模板
├── contact.css     # 组件样式文件
├── contact.js      # 组件加载器
├── test.html       # 测试页面
└── README.md       # 说明文档
```

## 使用方法

### 1. 引入CSS文件
在页面的`<head>`部分引入组件样式：
```html
<link rel="stylesheet" type="text/css" href="../component/contact/contact.css" />
```

### 2. 添加容器元素
在需要显示联系我们组件的位置添加容器：
```html
<!-- 联系我们组件容器 -->
<div id="contact-container"></div>
```

### 3. 引入JavaScript文件
在页面底部引入组件加载器：
```html
<script src="../component/contact/contact.js"></script>
```

## 组件特性
- **响应式设计**: 组件支持不同屏幕尺寸
- **自动加载**: 页面加载完成后自动加载组件内容
- **路径智能识别**: 根据当前页面路径自动调整组件文件路径
- **错误处理**: 包含加载失败的错误处理机制

## 样式定制
组件使用以下CSS类名，可以通过覆盖这些类来自定义样式：
- `.contact-section`: 主容器
- `.contact-header`: 标题区域
- `.contact-title`: 标题文字
- `.contact-content`: 内容区域
- `.contact-description`: 描述文字
- `.contact-btn`: 按钮样式

## 测试
打开 `test.html` 文件可以预览组件效果。

## 已应用页面
- home/index.html (首页)
- xiangqingye/index.html (产品详情页)

## 注意事项
- 确保背景图片路径正确 (../../home/<USER>/SketchPng1c26c087dcb080c171d06745e5afc6bf9e0a1bbd0f8e73514986c9c3afca5158.png)
- 组件依赖fetch API，需要在HTTP服务器环境下运行
- 如果需要在新页面中使用，请按照上述步骤进行集成
