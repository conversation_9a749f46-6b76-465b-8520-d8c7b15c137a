<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>联系我们组件测试</title>
		<!-- CSS现在由JavaScript自动加载，无需手动引入 -->
	</head>
	<body>
		<h1>联系我们组件测试页面</h1>
		<p>CSS文件会由JavaScript自动加载</p>

		<!-- 联系我们组件容器 -->
		<div id="contact-container"></div>

		<script>
			// 动态加载CSS文件
			function loadCSS(cssPath) {
				const existingLink = document.querySelector(`link[href="${cssPath}"]`);
				if (existingLink) {
					return;
				}
				const link = document.createElement("link");
				link.rel = "stylesheet";
				link.type = "text/css";
				link.href = cssPath;
				document.head.appendChild(link);
			}

			// 组件加载器（用于测试）
			function loadContact() {
				// 先加载CSS
				loadCSS("./contact.css");

				// 再加载HTML
				fetch("./contact.html")
					.then((response) => response.text())
					.then((html) => {
						const contactContainer =
							document.getElementById("contact-container");
						if (contactContainer) {
							contactContainer.innerHTML = html;
						}
					})
					.catch((error) => {
						console.error("联系我们组件加载失败:", error);
					});
			}

			// 页面加载完成后执行
			if (document.readyState === "loading") {
				document.addEventListener("DOMContentLoaded", loadContact);
			} else {
				loadContact();
			}
		</script>
	</body>
</html>
