// 联系我们组件加载器
(function () {
	// 获取组件路径
	function getComponentPaths() {
		const currentPath = window.location.pathname;
		// 根据当前路径确定组件的相对路径
		if (currentPath.includes("/home/")) {
			return {
				html: "../component/contact/contact.html",
				css: "../component/contact/contact.css",
			};
		} else if (currentPath.includes("/xiangqingye/")) {
			return {
				html: "../component/contact/contact.html",
				css: "../component/contact/contact.css",
			};
		} else if (currentPath.includes("/guanyuwomen/")) {
			return {
				html: "../component/contact/contact.html",
				css: "../component/contact/contact.css",
			};
		} else {
			// 默认路径
			return {
				html: "../component/contact/contact.html",
				css: "../component/contact/contact.css",
			};
		}
	}

	// 动态加载CSS文件
	function loadCSS(cssPath) {
		// 检查是否已经加载过该CSS文件
		const existingLink = document.querySelector(`link[href="${cssPath}"]`);
		if (existingLink) {
			return; // 已经加载过，不重复加载
		}

		const link = document.createElement("link");
		link.rel = "stylesheet";
		link.type = "text/css";
		link.href = cssPath;
		document.head.appendChild(link);
	}

	// 加载联系我们组件
	function loadContact() {
		const paths = getComponentPaths();

		// 先加载CSS
		loadCSS(paths.css);

		// 再加载HTML
		fetch(paths.html)
			.then((response) => response.text())
			.then((html) => {
				const contactContainer = document.getElementById("contact-container");
				if (contactContainer) {
					contactContainer.innerHTML = html;
				}
			})
			.catch((error) => {
				console.error("联系我们组件加载失败:", error);
			});
	}

	// 页面加载完成后执行
	if (document.readyState === "loading") {
		document.addEventListener("DOMContentLoaded", loadContact);
	} else {
		loadContact();
	}
})();
